import React from 'react'
import type { RewardSet } from '@repo/shared/features/rewards/types'
import { fetchRewardSets } from '@repo/shared/features/rewards/lib/api'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import RewardSetManager from './RewardSetManager'

export interface RewardSetsManagerProps {
  onSelect?: (set: RewardSet) => void
}

export const RewardSetsManager: React.FC<RewardSetsManagerProps> = ({ onSelect }) => {
  const [selectedId, setSelectedId] = React.useState<string | null>(null)
  const queryClient = useQueryClient()

  const { data: sets = [], isLoading: loading } = useQuery<RewardSet[]>({
    queryKey: ['reward-sets'],
    queryFn: () => fetchRewardSets(),
  })

  // Auto-select the first set when loaded
  React.useEffect(() => {
    if (!selectedId && sets.length) {
      setSelectedId(sets[0].id)
    }
  }, [sets, selectedId])

  const selectedSet = React.useMemo(() => sets.find((s) => s.id === selectedId) ?? null, [sets, selectedId])

  const handleSelect = (s: RewardSet) => {
    setSelectedId(s.id)
    onSelect?.(s)
  }

  const handleChange = (next: RewardSet) => {
    queryClient.setQueryData<RewardSet[]>(['reward-sets'], (prev) =>
      prev ? prev.map((s) => (s.id === next.id ? next : s)) : []
    )
  }

  return (
    <div className="w-full h-[800px] flex gap-6 overflow-hidden bg-background p-4">
      {/* Left sidebar */}
      <div className="w-[280px] shrink-0 h-full flex flex-col">
        <div className="text-lg font-semibold mb-4 text-foreground">Reward Sets</div>
        <div className="flex-1 overflow-y-auto space-y-1">
          {loading ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="animate-pulse">Loading reward sets...</div>
            </div>
          ) : sets.length ? (
            sets.map((s) => (
              <button
                key={s.id}
                type="button"
                onClick={() => handleSelect(s)}
                className={`w-full text-left p-3 rounded-lg transition-all duration-200 group ${
                  selectedId === s.id
                    ? 'bg-primary/10 border border-primary/30 shadow-sm'
                    : 'hover:bg-accent/60 border border-transparent'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className={`text-sm font-medium truncate ${selectedId === s.id ? 'text-primary' : 'text-foreground'}`}>
                      {s.id}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {s.rewards.length} {s.rewards.length === 1 ? 'reward' : 'rewards'}
                    </div>
                  </div>
                  {selectedId === s.id && (
                    <div className="w-2 h-2 rounded-full bg-primary shrink-0"></div>
                  )}
                </div>
              </button>
            ))
          ) : (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="text-center">
                <div className="text-sm">No reward sets found</div>
                <div className="text-xs mt-1">Create your first reward set to get started</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right detail view */}
      <div className="flex-1 min-w-0 h-full overflow-y-auto">
        {selectedSet ? (
          <div className="h-full">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-foreground mb-2">
                {selectedSet.id}
              </h2>
              <div className="text-sm text-muted-foreground">
                Manage rewards for this set
              </div>
            </div>
            <RewardSetManager set={selectedSet} onChange={handleChange} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-muted-foreground">
              <div className="text-lg mb-2">Select a reward set</div>
              <div className="text-sm">Choose a reward set from the sidebar to view and edit its rewards</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default RewardSetsManager


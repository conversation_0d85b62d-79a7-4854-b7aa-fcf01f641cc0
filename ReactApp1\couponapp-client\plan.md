# Declarative Game Screen Exposure in Node System

## Current State
- Reward-screen node uses imperative approach: `onInputConnected` → `addCustomScreen`
- Screens created via side effects during connection events
- UI editor reads screens from game config's `customScreens` array

## Goal
- Declarative/reactive screen exposure via node methods
- UI editor reads screens using flow hooks
- Remove imperative screen management

## Implementation Plan

### 1. Add Screen Exposure Method to Node Definition
- Add `exposeScreens?: (settings: TSettings, ctx: NodeScreenContext) => GameScreenDefinition[]` to NodeDefinition
- NodeScreenContext: `{ nodeId: string, flowGraphId: string, inputEdges: Edge[], outputEdges: Edge[] }`
- GameScreenDefinition: `{ id: string, name: string, type: 'custom' | 'builtin' }`

### 2. Create Flow Hook for Game Screens
- `useFlowGameScreens(flowId: string, gameWidgetId: string)` hook
- Scans flow graph for nodes with `exposeScreens` method
- Returns aggregated screen definitions
- Reactive to flow graph changes

### 3. Update Reward Screen Node
- Remove `onInputConnected`/`onInputDisconnected`
- Add `exposeScreens` method that returns screen based on current connections
- Screen ID generation based on connected edges

### 4. Update UI Editor Integration
- Replace `customScreensContext` usage with `useFlowGameScreens`
- Update screen selectors to use flow-derived screens
- Remove manual screen management

### 5. Flow Graph Analysis Utilities
- `getConnectedEdges(graph: FlowGraph, nodeId: string)` helper
- `findNodesWithScreens(graph: FlowGraph)` helper
- Edge data access for screen naming

### 6. Update Flow Editor
- Remove `customScreensContext` from node connection handlers
- Update node creation/deletion to trigger screen recalculation
- Ensure reactive updates when connections change

## Files to Modify
- `packages/shared/lib/flow/types.ts` - Add NodeScreenContext and exposeScreens to NodeDefinition
- `packages/shared/lib/flow/hooks/useFlowGameScreens.ts` - New hook
- `packages/shared/lib/flow/nodes/reward-screen/node.editor.tsx` - Update implementation
- `apps/dashboard/src/components/editor/flow-editor-new/FlowEditorNew.tsx` - Remove customScreensContext
- Game screen selectors - Update to use new hook

## Benefits
- Declarative screen definitions
- Automatic screen updates on flow changes
- Cleaner separation of concerns
- Reactive UI updates
